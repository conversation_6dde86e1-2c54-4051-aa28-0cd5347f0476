import "./App.css";

function App() {
  return (
    <>
      <header>
        <h1>Nakshatra V2</h1>
        <div>
          <button className="add-note-btn" onclick="openNoteDialog()">
            + Add Note
          </button>
          <button id="themeToggleBtn" className="theme-toggle-btn">
            🌙
          </button>
        </div>
      </header>

      <main id="notesContainer" className="notes-grid"></main>

      <dialog id="noteDialog">
        <div className="dialog-content">
          <div className="dialog-header">
            <h2 className="dialog-title" id="dialogTitle">
              Add New Note
            </h2>
            <button className="close-btn" onclick="closeNoteDialog()">
              x
            </button>
          </div>

          <form id="noteForm">
            <div className="form-group">
              <label for="noteTitle" className="form-label">
                Title
              </label>
              <input
                type="text"
                id="noteTitle"
                className="form-input"
                placeholder="Enter note title..."
                required
              />
            </div>

            <div className="form-group">
              <label for="noteContent" className="form-label">
                Content
              </label>
              <textarea
                id="noteContent"
                className="form-textarea"
                placeholder="Write your note here..."
                required
              ></textarea>
            </div>

            <div className="dialog-actions">
              <button
                type="button"
                className="cancel-btn"
                onclick="closeNoteDialog()"
              >
                Cancel
              </button>
              <button type="submit" className="save-btn">
                Save Note
              </button>
            </div>
          </form>
        </div>
      </dialog>
    </>
  );
}

export default App;
