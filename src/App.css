@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  --base-color: #f67700;

  --surface-color: oklch(from var(--base-color) calc(l * 1.2) c h);
  --brand-color: oklch(from var(--base-color) calc(l * 3) cal(c * 5) h);
  --text-color: #f0f0f0;
  --secondary-text-color: #b0b3c0;
  --base-transparent-90: rgba(30, 31, 38, 0.9);
  --base-transparent-100: rgba(30, 31, 38, 1);

  font-family: Poppins, sans-serif;
  margin: 2rem;
  background: var(--base-color);
  color: var(--text-color);
}

h1 {
  margin-bottom: 1rem;
}

button,
input,
textarea {
  font: inherit;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.add-note-btn {
  background: var(--brand-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.add-note-btn:hover {
  background: #7a7fff;
}

.theme-toggle-btn {
  background: var(--surface-color);
  color: var(--text-color);
  border: 1px solid var(--surface-color);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-left: 1rem;
}

.theme-toggle-btn:hover {
  background: var(--brand-color);
  color: white;
}


.notes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
}

.note-card {
  background: var(--surface-color);
  border-radius: 0.75rem;
  padding: 1.5rem;
  border: 1px solid var(--surface-color);
  transition: all 0.2s ease;
  position: relative;
}

.note-card:hover {
  transform: scale(1.02);
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.05);
}

.note-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.75rem;
  color: var(--text-color);
  word-wrap: break-word;
}

.note-content {
  color: var(--secondary-text-color);
  line-height: 1.6;
  margin-bottom: 1rem;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.note-actions {
  position: absolute;
  top: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
  opacity: 0;
  visibility: hidden;
  transition: all 0.2s ease;
}

.note-card:hover .note-actions {
  opacity: 1;
  visibility: visible;
}

.edit-btn,
.delete-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
  background: var(--base-transparent-90);
  color: var(--text-color);
  border: 1px solid var(--surface-color);
}

.edit-btn:hover {
  background: var(--base-transparent-100);
  transform: scale(1.05);
}

.delete-btn:hover {
  background: #ff5252;
  transform: scale(1.05);
  color: white;
}

dialog {
  margin: auto;
  inset: 0;
  border: none;
  border-radius: 1rem;
  padding: 0;
  background: var(--surface-color);
  color: var(--text-color);
  max-width: 500px;
  width: 90vw;
}

dialog::backdrop {
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(4px);
}

.dialog-content {
  padding: 2rem;
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.dialog-title {
  font-size: 1.5rem;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: var(--secondary-text-color);
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: background 0.2s ease;
}

.close-btn:hover {
  background: var(--surface-color);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: var(--text-color);
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--surface-color);
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.2s ease;
  background: var(--base-color);
  color: var(--text-color);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--brand-color);
}

.form-textarea {
  resize: vertical;
  min-height: 120px;
}

.dialog-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn,
.save-btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.cancel-btn {
  background: var(--base-color);
  color: var(--text-color);
}

.save-btn {
  background: var(--brand-color);
  color: white;
}

.save-btn:hover {
  background: #7a7fff;
}

.empty-state {
  text-align: center;
  padding: 4rem 2rem;
  color: var(--secondary-text-color);
}

.empty-state h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

.empty-state p {
  font-size: 1rem;
  margin-bottom: 2rem;
}