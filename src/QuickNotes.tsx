import React, { useState, useEffect, useRef } from 'react';

function generateId() {
  return Date.now().toString();
}

const getStoredNotes = () => {
  const saved = localStorage.getItem('quickNotes');
  return saved ? JSON.parse(saved) : [];
};

const getStoredTheme = () => {
  return localStorage.getItem('theme') === 'dark';
};

export default function QuickNotes() {
  const [notes, setNotes] = useState([]);
  const [editingNoteId, setEditingNoteId] = useState(null);
  const [showDialog, setShowDialog] = useState(false);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [darkTheme, setDarkTheme] = useState(getStoredTheme());
  const titleInputRef = useRef(null);

  useEffect(() => {
    setNotes(getStoredNotes());
  }, []);

  useEffect(() => {
    document.body.classList.toggle('dark', darkTheme);
    localStorage.setItem('theme', darkTheme ? 'dark' : 'light');
  }, [darkTheme]);

  useEffect(() => {
    if (showDialog && titleInputRef.current) {
      titleInputRef.current.focus();
    }
  }, [showDialog]);

  const saveNotes = (notesArr) => {
    localStorage.setItem('quickNotes', JSON.stringify(notesArr));
  };

  const handleSaveNote = (e) => {
    e.preventDefault();
    if (!title.trim() || !content.trim()) return;
    let updatedNotes;
    if (editingNoteId) {
      updatedNotes = notes.map(note =>
        note.id === editingNoteId ? { ...note, title, content } : note
      );
    } else {
      updatedNotes = [
        { id: generateId(), title, content },
        ...notes,
      ];
    }
    setNotes(updatedNotes);
    saveNotes(updatedNotes);
    setShowDialog(false);
    setEditingNoteId(null);
    setTitle('');
    setContent('');
  };

  const handleDeleteNote = (id) => {
    const updatedNotes = notes.filter(note => note.id !== id);
    setNotes(updatedNotes);
    saveNotes(updatedNotes);
  };

  const openNoteDialog = (id = null) => {
    if (id) {
      const note = notes.find(n => n.id === id);
      setEditingNoteId(id);
      setTitle(note.title);
      setContent(note.content);
    } else {
      setEditingNoteId(null);
      setTitle('');
      setContent('');
    }
    setShowDialog(true);
  };

  const closeNoteDialog = () => {
    setShowDialog(false);
    setEditingNoteId(null);
    setTitle('');
    setContent('');
  };

  const toggleTheme = () => {
    setDarkTheme(prev => !prev);
  };

  return (
    <div className={`min-h-screen px-8 py-8 font-sans ${darkTheme ? 'bg-gray-900 text-gray-100' : 'bg-orange-100 text-gray-900'}`}>
      <header className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Quick Notes</h1>
        <div className="flex gap-4">
          <button
            className="bg-orange-500 hover:bg-indigo-500 text-white px-4 py-2 rounded font-medium transition"
            onClick={() => openNoteDialog()}
          >
            + Add Note
          </button>
          <button
            className="bg-orange-200 dark:bg-gray-800 border border-orange-200 dark:border-gray-700 text-xl px-4 py-2 rounded transition"
            onClick={toggleTheme}
            title="Toggle Theme"
          >
            {darkTheme ? '☀️' : '🌙'}
          </button>
        </div>
      </header>

      <main className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6" id="notesContainer">
        {notes.length === 0 ? (
          <div className="col-span-full text-center py-16 text-gray-400">
            <h2 className="text-2xl font-semibold mb-2">No notes yet</h2>
            <p className="mb-6">Create your first note to get started!</p>
            <button
              className="bg-indigo-500 hover:bg-orange-500 text-white px-4 py-2 rounded font-medium transition"
              onClick={() => openNoteDialog()}
            >
              + Add Your First Note
            </button>
          </div>
        ) : (
          notes.map(note => (
            <div key={note.id} className="relative bg-white dark:bg-gray-800 border border-orange-200 dark:border-gray-700 rounded-lg p-6 shadow hover:scale-105 transition">
              <h3 className="text-lg font-semibold mb-2 break-words text-orange-700 dark:text-orange-300">{note.title}</h3>
              <p className="text-gray-600 dark:text-gray-300 mb-4 whitespace-pre-wrap break-words">{note.content}</p>
              <div className="absolute top-4 right-4 flex gap-2 opacity-0 group-hover:opacity-100 transition">
                <button
                  className="w-8 h-8 flex items-center justify-center bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                  onClick={() => openNoteDialog(note.id)}
                  title="Edit Note"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                  </svg>
                </button>
                <button
                  className="w-8 h-8 flex items-center justify-center bg-gray-100 dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded hover:bg-red-500 hover:text-white"
                  onClick={() => handleDeleteNote(note.id)}
                  title="Delete Note"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M18.3 5.71c-.39-.39-1.02-.39-1.41 0L12 10.59 7.11 5.7c-.39-.39-1.02-.39-1.41 0-.39.39-.39 1.02 0 1.41L10.59 12 5.7 16.89c-.39.39-.39 1.02 0 1.41.39.39 1.02.39 1.41 0L12 13.41l4.89 4.88c.39.39 1.02.39 1.41 0 .39-.39.39-1.02 0-1.41L13.41 12l4.89-4.89c.38-.38.38-1.02 0-1.4z"/>
                  </svg>
                </button>
              </div>
            </div>
          ))
        )}
      </main>

      {/* Dialog */}
      {showDialog && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30 backdrop-blur-sm">
          <div className="bg-white dark:bg-gray-900 rounded-xl shadow-lg max-w-md w-full p-8 relative">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold">{editingNoteId ? 'Edit Note' : 'Add New Note'}</h2>
              <button
                className="text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 rounded p-1 text-2xl"
                onClick={closeNoteDialog}
                title="Close"
              >
                ×
              </button>
            </div>
            <form onSubmit={handleSaveNote}>
              <div className="mb-4">
                <label htmlFor="noteTitle" className="block mb-2 font-medium">Title</label>
                <input
                  id="noteTitle"
                  ref={titleInputRef}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:border-orange-500 bg-orange-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  placeholder="Enter note title..."
                  value={title}
                  onChange={e => setTitle(e.target.value)}
                  required
                />
              </div>
              <div className="mb-6">
                <label htmlFor="noteContent" className="block mb-2 font-medium">Content</label>
                <textarea
                  id="noteContent"
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded focus:outline-none focus:border-orange-500 bg-orange-50 dark:bg-gray-800 text-gray-900 dark:text-gray-100 resize-vertical min-h-[120px]"
                  placeholder="Write your note here..."
                  value={content}
                  onChange={e => setContent(e.target.value)}
                  required
                />
              </div>
              <div className="flex gap-4 justify-end">
                <button
                  type="button"
                  className="bg-orange-100 dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-4 py-2 rounded font-medium transition"
                  onClick={closeNoteDialog}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-orange-500 hover:bg-indigo-500 text-white px-4 py-2 rounded font-medium transition"
                >
                  Save Note
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
